
# AI Agent Utils

A shared library for AI chatbot agents.

## Installation

To install ai-agent-utils (from PyPI if published):

```bash
pip install ai-agent-utils
```

## Local Development

### 1. Install Dependencies
Create and activate a virtual environment, then install development dependencies:
```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements-dev.txt
```

### 2. Install Package Locally (Editable Mode)
```bash
pip install -e .
```

### 3. Run Tests
```bash
pytest tests/
```

## Build for Release and Publish to Nexus

1. Install build tools and twine:
    ```bash
    pip install build twine
    ```
2. Build distribution files:
    ```bash
    python -m build
    ```
3. Publish to Nexus:
    ```bash
    python -m twine upload --repository-url https://nexus.lifetime.life/repository/lttpip-publish/ dist/* -u enumber -p 'password'
    ```